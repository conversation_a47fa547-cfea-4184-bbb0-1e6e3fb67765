import { Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { GameService } from '../../core/services/game.service';
import { CartService } from '../../core/services/cart.service';
import { ModalService } from '../../core/services/modal.service';
import { Game } from '../../core/models/game.model';

@Component({
  selector: 'app-game-detail',
  standalone: false,
  templateUrl: './game-detail.component.html',
  styleUrl: './game-detail.component.css'
})
export class GameDetailComponent implements OnInit, OnDestroy {
  game: Game | null = null;
  loading = false;
  error = '';
  gameId: number | null = null;

  // Cart
  cartItemCount = 0;
  private cartSubscription?: Subscription;
  private cartChangesSubscription?: Subscription;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private gameService: GameService,
    private cartService: CartService,
    private modalService: ModalService
  ) {}

  ngOnInit(): void {
    this.route.params.subscribe(params => {
      this.gameId = +params['id'];
      if (this.gameId) {
        this.loadGame();
      }
    });

    this.setupCartSubscription();
    this.setupCartChangesSubscription();
  }

  ngOnDestroy(): void {
    this.cartSubscription?.unsubscribe();
    this.cartChangesSubscription?.unsubscribe();
  }

  private setupCartSubscription(): void {
    this.cartSubscription = this.cartService.cart$.subscribe(cart => {
      this.cartItemCount = cart.total_items;
    });
  }

  private setupCartChangesSubscription(): void {
    this.cartChangesSubscription = this.cartService.cartChanges$.subscribe(change => {
      // Update the current game's is_in_cart status if it matches
      if (this.game && this.game.id === change.gameId) {
        this.game.is_in_cart = change.action === 'added';
      }
    });
  }

  loadGame(): void {
    if (!this.gameId) return;

    this.loading = true;
    this.error = '';

    this.gameService.getGame(this.gameId).subscribe({
      next: (game) => {
        this.game = game;
        this.loading = false;
      },
      error: (error) => {
        this.error = error.message || 'Не удалось загрузить информацию об игре';
        this.loading = false;
      }
    });
  }

  addToCart(): void {
    if (this.game) {
      this.cartService.addToCart(this.game).subscribe({
        next: () => {
          // Success - cart will be automatically updated via subscription
          // Update the local game object to reflect the cart status immediately
          if (this.game) {
            this.game.is_in_cart = true;
          }
          console.log('Game added to cart successfully');
        },
        error: (error) => {
          console.error('Error adding game to cart:', error.message);
          this.modalService.error('Ошибка', 'Не удалось добавить игру в корзину: ' + error.message);
        }
      });
    }
  }

  isInCart(): boolean {
    if (!this.game) return false;

    // Use the is_in_cart field from the API response if available (when user is authenticated)
    // Fall back to cart service check if not available (when user is not authenticated)
    return this.game.is_in_cart !== undefined ? this.game.is_in_cart : this.cartService.isInCart(this.game.id);
  }

  goBack(): void {
    this.router.navigate(['/games']);
  }

  formatPrice(price: string): string {
    const numPrice = parseFloat(price);
    return numPrice.toLocaleString('ru-RU') + ' ₸';
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }
}
